<?php
/**
 * Test file for PWA Analytics functionality
 * This file tests the PWA analytics shortcodes and database functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll simulate WordPress environment
    define('ABSPATH', dirname(__FILE__) . '/');
    
    // Mock WordPress functions for testing
    if (!function_exists('wp_send_json_success')) {
        function wp_send_json_success($data) {
            echo json_encode(['success' => true, 'data' => $data]);
            exit;
        }
    }
    
    if (!function_exists('wp_send_json_error')) {
        function wp_send_json_error($data) {
            echo json_encode(['success' => false, 'data' => $data]);
            exit;
        }
    }
    
    if (!function_exists('sanitize_text_field')) {
        function sanitize_text_field($str) {
            return htmlspecialchars(strip_tags($str), ENT_QUOTES, 'UTF-8');
        }
    }
    
    if (!function_exists('current_time')) {
        function current_time($type) {
            return date('Y-m-d H:i:s');
        }
    }
    
    if (!function_exists('shortcode_atts')) {
        function shortcode_atts($defaults, $atts, $shortcode = '') {
            return array_merge($defaults, (array) $atts);
        }
    }
    
    if (!function_exists('number_format_i18n')) {
        function number_format_i18n($number) {
            return number_format($number);
        }
    }
}

/**
 * Test PWA Analytics Shortcode Functionality
 */
function test_pwa_analytics_shortcodes() {
    echo "<h2>Testing PWA Analytics Shortcodes</h2>\n";
    
    // Test 1: Check if [pwa_analytics] shortcode is registered
    echo "<h3>Test 1: Shortcode Registration</h3>\n";
    
    if (function_exists('q_pwa_analytics_shortcode')) {
        echo "✅ q_pwa_analytics_shortcode function exists\n<br>";
        
        // Test with different parameters
        $test_cases = [
            ['event_type' => 'install', 'period' => 'week', 'format' => 'number'],
            ['event_type' => 'pageview', 'period' => 'today', 'format' => 'number'],
            ['event_type' => 'session_start', 'period' => 'month', 'format' => 'percentage'],
            ['event_type' => '', 'period' => 'all', 'format' => 'number'], // Should return empty
        ];
        
        foreach ($test_cases as $i => $test_case) {
            echo "Test case " . ($i + 1) . ": ";
            $result = q_pwa_analytics_shortcode($test_case);
            if ($test_case['event_type'] === '' && $result === '') {
                echo "✅ Correctly returns empty for missing event_type\n<br>";
            } else {
                echo "Result: " . ($result !== false ? $result : 'No data') . "\n<br>";
            }
        }
    } else {
        echo "❌ q_pwa_analytics_shortcode function not found\n<br>";
    }
    
    // Test 2: Check if [pwa_user_analytics] shortcode is registered
    echo "<h3>Test 2: User Analytics Shortcode</h3>\n";
    
    if (class_exists('Q_PWA_Analytics') && method_exists('Q_PWA_Analytics', 'user_analytics_shortcode')) {
        echo "✅ Q_PWA_Analytics::user_analytics_shortcode method exists\n<br>";
        
        // Test with different parameters
        $user_test_cases = [
            ['user_id' => '1', 'event' => 'online'],
            ['user_id' => '1', 'event' => 'devices'],
            ['user_id' => '1', 'event' => 'push_enabled'],
            ['user_id' => '', 'event' => 'online'], // Should return error
        ];
        
        foreach ($user_test_cases as $i => $test_case) {
            echo "User test case " . ($i + 1) . ": ";
            $result = Q_PWA_Analytics::user_analytics_shortcode($test_case);
            echo "Result: " . $result . "\n<br>";
        }
    } else {
        echo "❌ Q_PWA_Analytics::user_analytics_shortcode method not found\n<br>";
    }
}

/**
 * Test Database Table Structure
 */
function test_database_tables() {
    echo "<h2>Testing Database Tables</h2>\n";
    
    // Check if we can access the database
    global $wpdb;
    if (!isset($wpdb)) {
        echo "❌ WordPress database object not available\n<br>";
        return;
    }
    
    $table_name = $wpdb->prefix . 'q_pwa_analytics';
    
    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_exists) {
        echo "✅ Table $table_name exists\n<br>";
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        echo "<h3>Table Structure:</h3>\n";
        echo "<table border='1'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column->Field . "</td>";
            echo "<td>" . $column->Type . "</td>";
            echo "<td>" . $column->Null . "</td>";
            echo "<td>" . $column->Key . "</td>";
            echo "<td>" . $column->Default . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Check if we can insert test data
        $test_data = [
            'event_name' => 'test_event',
            'user_id' => 1,
            'device_type' => 'desktop',
            'event_data' => json_encode(['test' => 'data']),
            'timestamp' => current_time('mysql')
        ];
        
        $result = $wpdb->insert($table_name, $test_data);
        if ($result !== false) {
            echo "✅ Successfully inserted test data\n<br>";
            
            // Clean up test data
            $wpdb->delete($table_name, ['event_name' => 'test_event']);
            echo "✅ Test data cleaned up\n<br>";
        } else {
            echo "❌ Failed to insert test data: " . $wpdb->last_error . "\n<br>";
        }
        
    } else {
        echo "❌ Table $table_name does not exist\n<br>";
        
        // Try to create the table
        if (class_exists('Q_PWA_Analytics')) {
            echo "Attempting to create table...\n<br>";
            Q_PWA_Analytics::create_tables();
            
            // Check again
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            if ($table_exists) {
                echo "✅ Table created successfully\n<br>";
            } else {
                echo "❌ Failed to create table\n<br>";
            }
        }
    }
}

/**
 * Test Analytics Helper Functions
 */
function test_analytics_functions() {
    echo "<h2>Testing Analytics Helper Functions</h2>\n";
    
    // Test q_get_pwa_analytics function
    if (function_exists('q_get_pwa_analytics')) {
        echo "✅ q_get_pwa_analytics function exists\n<br>";
        
        $analytics_data = q_get_pwa_analytics('week', 10);
        echo "Analytics data for last week: " . count($analytics_data) . " records\n<br>";
        
        if (!empty($analytics_data)) {
            echo "<h4>Sample Analytics Data:</h4>\n";
            echo "<pre>" . print_r(array_slice($analytics_data, 0, 3), true) . "</pre>\n";
        }
    } else {
        echo "❌ q_get_pwa_analytics function not found\n<br>";
    }
    
    // Test q_get_pwa_analytics_chart_data function
    if (function_exists('q_get_pwa_analytics_chart_data')) {
        echo "✅ q_get_pwa_analytics_chart_data function exists\n<br>";
        
        $chart_data = q_get_pwa_analytics_chart_data('install', 'week', 7);
        echo "Chart data for installs: " . count($chart_data) . " data points\n<br>";
    } else {
        echo "❌ q_get_pwa_analytics_chart_data function not found\n<br>";
    }
}

/**
 * Test AJAX Endpoints
 */
function test_ajax_endpoints() {
    echo "<h2>Testing AJAX Endpoints</h2>\n";
    
    // Check if AJAX handlers are registered
    $ajax_actions = [
        'q_pwa_get_analytics',
        'q_pwa_export_analytics', 
        'q_track_pwa_event',
        'q_pwa_track_event'
    ];
    
    foreach ($ajax_actions as $action) {
        if (has_action("wp_ajax_$action") || has_action("wp_ajax_nopriv_$action")) {
            echo "✅ AJAX action '$action' is registered\n<br>";
        } else {
            echo "❌ AJAX action '$action' is not registered\n<br>";
        }
    }
}

// Run tests if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    echo "<h1>PWA Analytics Test Results</h1>\n";
    
    // Include the necessary files
    if (file_exists('includes/notification-analytics.php')) {
        require_once 'includes/notification-analytics.php';
    }
    
    if (file_exists('includes/class-q-pwa-analytics.php')) {
        require_once 'includes/class-q-pwa-analytics.php';
    }
    
    test_pwa_analytics_shortcodes();
    test_database_tables();
    test_analytics_functions();
    test_ajax_endpoints();
    
    echo "<h2>Test Complete</h2>\n";
}
?>
