/**
 * PWA Styles - Progressive Web App specific styles
 */

/* PWA Install Prompt */
.q-pwa-install-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.q-pwa-install-container.hidden {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}

/* iOS Install Instructions */
.q-pwa-ios-instructions {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.q-pwa-ios-instructions-content {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 350px;
  position: relative;
}

.q-pwa-ios-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.q-pwa-ios-instructions-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
}

.q-pwa-ios-instructions-content ol {
  margin: 0;
  padding-left: 20px;
}

.q-pwa-ios-instructions-content li {
  margin-bottom: 10px;
}

.ios-share-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-left: 5px;
}

.q-pwa-install-prompt {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.q-pwa-install-content {
  background: linear-gradient(135deg, #fff, rgba(255,255,255,0.5));
  backdrop-filter: blur(16px);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.q-pwa-install-icon {
  font-size: 48px;
  flex-shrink: 0;
}

.q-pwa-install-text {
  flex: 1;
}

.q-pwa-install-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.q-pwa-install-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.q-pwa-install-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.q-pwa-btn-primary {
    font-size: 13px;
    display: inline-block;
    border: 1px solid #D0D5DD;
    padding: 8px 15px;
    border-radius: 8px;
    box-shadow: 0px 0px 1px rgba(16, 24, 40, 0.18) inset, 0px 1px 2px 0px rgba(16, 24, 40, 0.05) !important;
    transition: all .2s ease;
    background: #FFF;
    color: #344054;
    font-weight: 500;
    cursor: pointer;
}

.q-pwa-btn-primary:hover {
  background: #005a87;
}

.q-pwa-btn-secondary {
  background: transparent;
  color: #000;
  border: 1px solid #000;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.q-pwa-btn-secondary:hover {
  background: #000;
  color: #fff;
}


/* PWA App Shell */
.pwa-enabled {
  /* Base styles for PWA-enabled sites */
}

.pwa-installed {
  /* Styles when app is installed */
}

.pwa-installed .site-header {
  /* Adjust header for standalone mode */
  padding-top: env(safe-area-inset-top);
}

.pwa-installed .site-footer {
  /* Adjust footer for standalone mode */
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS Standalone Mode */
.ios-standalone {
  /* iOS-specific standalone styles */
}

.ios-standalone .site-header {
  padding-top: 44px; /* Status bar height */
}

/* Responsive Design for PWA */
@media (display-mode: standalone) {
  /* Styles when running as installed PWA */
  body {
    user-select: none; /* Prevent text selection in app mode */
  }

  /* Hide browser-specific elements */
  .browser-only {
    display: none !important;
  }

  /* Show PWA-specific elements */
  .pwa-only {
    display: block !important;
  }
}

@media (display-mode: fullscreen) {
  /* Styles for fullscreen mode */
  .site-header,
  .site-footer {
    display: none;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .q-pwa-install-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .q-pwa-install-actions {
    flex-direction: row;
    justify-content: center;
    width: 100%;
  }



}

@media (max-width: 480px) {
  .q-pwa-install-prompt {
    padding: 15px;
  }

  .q-pwa-install-content {
    padding: 15px;
  }

  .q-pwa-install-text h3 {
    font-size: 16px;
  }

  .q-pwa-install-text p {
    font-size: 13px;
  }

  .q-pwa-btn-primary {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Contact Picker Styles */
.q-pwa-contact-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.q-pwa-contact-container.hidden {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}

.q-pwa-contact-prompt {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.q-pwa-contact-content {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.q-pwa-contact-icon {
  font-size: 48px;
  flex-shrink: 0;
}

.q-pwa-contact-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.q-pwa-contact-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.q-pwa-contact-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}





@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}



@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .q-pwa-install-icon {
    /* High-resolution icon styles */
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .q-pwa-contact-content {
    background: #2c2c2c;
    color: #fff;
  }

  .q-pwa-install-text h3 {
    color: #fff;
  }

  .q-pwa-install-text p {
    color: #ccc;
  }
}

/* Print Styles */
@media print {
  .q-pwa-contact-container,
  .q-pwa-install-container {
    display: none !important;
  }
}
