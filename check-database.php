<?php
// Include WordPress
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

// Check if table exists
global $wpdb;
$table_name = $wpdb->prefix . 'q_pwa_analytics';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "✅ Table $table_name exists\n";

    // Get table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo "\nTable Structure:\n";
    echo "Field\t\tType\t\tNull\tKey\tDefault\n";
    echo "-----\t\t----\t\t----\t---\t-------\n";
    foreach ($columns as $column) {
        echo $column->Field . "\t\t" . $column->Type . "\t" . $column->Null . "\t" . $column->Key . "\t" . $column->Default . "\n";
    }

    // Check record count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "\nTotal records: $count\n";

    // Show sample data if any exists
    if ($count > 0) {
        $sample = $wpdb->get_results("SELECT * FROM $table_name ORDER BY timestamp DESC LIMIT 3");
        echo "\nSample records:\n";
        foreach ($sample as $record) {
            echo "ID: $record->id, Event: $record->event_name, User: $record->user_id, Time: $record->timestamp\n";
        }
    }
} else {
    echo "❌ Table $table_name does not exist\n";

    // Try to create it
    if (class_exists('Q_PWA_Analytics')) {
        echo "Attempting to create table...\n";
        Q_PWA_Analytics::create_tables();

        // Check again
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if ($table_exists) {
            echo "✅ Table created successfully\n";
        } else {
            echo "❌ Failed to create table\n";
        }
    } else {
        echo "❌ Q_PWA_Analytics class not found\n";
    }
}
