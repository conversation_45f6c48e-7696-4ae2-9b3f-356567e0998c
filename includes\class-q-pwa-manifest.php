<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Manifest
{
    public static function init()
    {
        add_action('init', [self::class, 'add_manifest_rewrite_rule']);
        add_action('init', [self::class, 'serve_manifest']);
        add_action('template_redirect', [self::class, 'serve_manifest']);
        add_filter('query_vars', [self::class, 'add_manifest_query_var']);

        // Also handle direct requests
        add_action('wp', [self::class, 'handle_manifest_request']);
        
        // Add AJAX handler for flushing manifest cache
        add_action('wp_ajax_q_flush_manifest_cache', [self::class, 'ajax_flush_manifest_cache']);
        add_action('wp_ajax_nopriv_q_flush_manifest_cache', [self::class, 'ajax_flush_manifest_cache']);
        
        // Include the shortcut handler
        require_once dirname(__FILE__) . '/pwa-shortcut-handler.php';
    }
    
    /**
     * AJAX handler for flushing manifest cache
     */
    public static function ajax_flush_manifest_cache()
    {
        self::flush_manifest_cache();
        wp_send_json_success(['success' => true]);
    }

    public static function add_manifest_rewrite_rule()
    {
        add_rewrite_rule('^manifest\.json$', 'index.php?q_manifest=1', 'top');
    }

    public static function add_manifest_query_var($vars)
    {
        $vars[] = 'q_manifest';
        return $vars;
    }

    public static function serve_manifest()
    {
        if (get_query_var('q_manifest')) {
            self::output_manifest();
        }
    }

    public static function handle_manifest_request()
    {
        // Check if this is a manifest.json request
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($request_uri, '/manifest.json') !== false) {
            self::output_manifest();
        }
    }

    private static function output_manifest()
    {
        // Set headers
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('Access-Control-Allow-Origin: *');

        // Generate and output manifest
        $manifest = self::generate_manifest();
        
        // Add a version field to force updates
        $manifest['version'] = get_option('q_pwa_manifest_updated', time());
        
        // Ensure required fields are present
        $manifest['display'] = $manifest['display'] ?? 'standalone';
        $manifest['start_url'] = $manifest['start_url'] ?? '/';
        $manifest['background_color'] = $manifest['background_color'] ?? '#ffffff';
        $manifest['theme_color'] = $manifest['theme_color'] ?? '#000000';

        // Ensure at least 192x192 and 512x512 icons
        $has_192 = false;
        $has_512 = false;
        foreach ($manifest['icons'] as $icon) {
            if (strpos($icon['sizes'], '192x192') !== false) $has_192 = true;
            if (strpos($icon['sizes'], '512x512') !== false) $has_512 = true;
        }
        if (!$has_192 && !empty($manifest['icons'])) {
            $manifest['icons'][] = [
                'src' => $manifest['icons'][0]['src'],
                'sizes' => '192x192',
                'type' => $manifest['icons'][0]['type'] ?? 'image/png'
            ];
        }
        if (!$has_512 && !empty($manifest['icons'])) {
            $manifest['icons'][] = [
                'src' => $manifest['icons'][0]['src'],
                'sizes' => '512x512',
                'type' => $manifest['icons'][0]['type'] ?? 'image/png'
            ];
        }
        
        echo json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        exit;
    }

    public static function generate_manifest()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return [
                'name' => get_bloginfo('name'),
                'short_name' => get_bloginfo('name'),
                'description' => get_bloginfo('description'),
                'start_url' => '/',
                'display' => 'browser',
                'theme_color' => '#000000',
                'background_color' => '#ffffff',
                'icons' => []
            ];
        }

        // Always use root as scope to avoid scope issues
        $start_url = get_option('q_pwa_start_url', '/');
        $scope = '/';

        // Ensure background color is a valid hex color
        $background_color = get_option('q_pwa_background_color', '#ffffff');
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $background_color)) {
            $background_color = '#ffffff';
        }
        
        // Ensure theme color is a valid hex color
        $theme_color = get_option('q_pwa_theme_color', '#000000');
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $theme_color)) {
            $theme_color = '#000000';
        }
        
        $manifest = [
            'name' => get_option('q_pwa_app_name', get_bloginfo('name')),
            'short_name' => get_option('q_pwa_app_short_name', get_bloginfo('name')),
            'description' => get_option('q_pwa_app_description', get_bloginfo('description')),
            'start_url' => $start_url,
            'display' => get_option('q_pwa_display_mode', 'standalone'),
            'orientation' => get_option('q_pwa_orientation', 'any'),
            'theme_color' => $theme_color,
            'background_color' => $background_color,
            'scope' => $scope,
            'lang' => get_locale(),
            'dir' => is_rtl() ? 'rtl' : 'ltr',
            'categories' => self::get_app_categories(),
            'icons' => self::generate_icons()
        ];

        // Add advanced features
        $manifest = self::add_shortcuts($manifest);
        $manifest = self::add_share_target($manifest);
        $manifest = self::add_file_handlers($manifest);
        $manifest = self::add_protocol_handlers($manifest);
        $manifest = self::add_display_override($manifest);
        $manifest = self::add_launch_handler($manifest);

        // Add screenshots if available
        $screenshots = self::generate_screenshots();
        if (!empty($screenshots)) {
            $manifest['screenshots'] = $screenshots;
        }

        return apply_filters('q_pwa_manifest', $manifest);
    }

    public static function generate_icons()
    {
        $icons = [];

        // Get the main icon URL selected in settings (expected to be a square PNG)
        $main_icon = get_option('q_pwa_icon', '');

        if ($main_icon) {
            // Ensure URL is absolute
            if (!preg_match('/^https?:\/\//', $main_icon)) {
                $main_icon = site_url($main_icon);
            }

            // Try to detect the *actual* dimensions of the image so the `sizes` attribute
            // reflects reality and Chrome DevTools will not complain.
            $icon_sizes_value = '';
            [$img_w, $img_h]  = self::detect_image_size($main_icon);
            if ($img_w && $img_h) {
                $icon_sizes_value = $img_w . 'x' . $img_h;
            }

            // Fallback when we cannot detect size (eg. remote URL denied by server)
            if (empty($icon_sizes_value)) {
                $icon_sizes_value = '512x512';
            }

            // Primary "any" purpose icon
            $icons[] = [
                'src'    => $main_icon,
                'sizes'  => $icon_sizes_value,
                'type'   => 'image/png',
                'purpose'=> 'any',
            ];

            // Maskable version (same source). Browsers will only pick it up when needed.
            $icons[] = [
                'src'    => $main_icon,
                'sizes'  => $icon_sizes_value,
                'type'   => 'image/png',
                'purpose'=> 'maskable',
            ];
        }

        /*
         * Optional bespoke icons defined in settings
         * ────────────────────────────────────────────
         * When the user explicitly sets 192×192 or 512×512 variants we honour them
         * with their *real* dimensions so they pass the manifest audit.
         */
        foreach ([
            'q_pwa_icon_192' => 'any', // 192×192
            'q_pwa_icon_512' => 'any', // 512×512
            'q_pwa_maskable_icon' => 'maskable', // custom maskable
        ] as $option_key => $purpose) {
            $custom_icon = get_option($option_key, '');
            if (empty($custom_icon)) {
                continue;
            }

            if (!preg_match('/^https?:\/\//', $custom_icon)) {
                $custom_icon = site_url($custom_icon);
            }

            [$cw, $ch]  = self::detect_image_size($custom_icon);
            $sizes_val  = ($cw && $ch)
                ? $cw . 'x' . $ch
                : ($option_key === 'q_pwa_icon_192' ? '192x192' : '512x512');

            // Prevent duplicates for the same size & purpose
            $icons = array_filter($icons, function ($i) use ($sizes_val, $purpose) {
                return !($i['sizes'] === $sizes_val && $i['purpose'] === $purpose);
            });

            $icons[] = [
                'src'     => $custom_icon,
                'sizes'   => $sizes_val,
                'type'    => 'image/png',
                'purpose' => $purpose,
            ];
        }

        // Automatically generate 192x192 and 512x512 icons if a main icon is set and these sizes are missing.
        if ($main_icon) {
            if (!self::icon_size_exists($icons, '192x192')) {
                $resized_192 = self::generate_resized_icon($main_icon, 192, 192);
                if ($resized_192) {
                    $icons[] = $resized_192;
                }
            }
            if (!self::icon_size_exists($icons, '512x512')) {
                $resized_512 = self::generate_resized_icon($main_icon, 512, 512);
                if ($resized_512) {
                    $icons[] = $resized_512;
                }
            }
        }

        // Sort icons so smaller come first – purely cosmetic but nice.
        usort($icons, function ($a, $b) {
            $aw = intval(explode('x', $a['sizes'])[0]);
            $bw = intval(explode('x', $b['sizes'])[0]);
            return $aw <=> $bw;
        });

        // Absolute last-chance fallback – use the WordPress Site Icon if everything
        // above yielded nothing. This prevents the manifest from being totally empty.
        if (empty($icons)) {
            $site_icon_id = get_option('site_icon');
            if ($site_icon_id) {
                $site_icon_url = wp_get_attachment_image_url($site_icon_id, 'full');
                if ($site_icon_url) {
                    [$sw, $sh] = self::detect_image_size($site_icon_url);
                    $size_val  = ($sw && $sh) ? $sw . 'x' . $sh : '512x512';
                    $icons[] = [
                        'src'    => $site_icon_url,
                        'sizes'  => $size_val,
                        'type'   => 'image/png',
                        'purpose'=> 'any',
                    ];
                }
            }
        }

        return $icons;
    }

    /**
     * Generate a resized icon from a source URL.
     *
     * @param string $source_url The URL of the source image.
     * @param int $width The desired width.
     * @param int $height The desired height.
     * @return array|false An icon array on success, false on failure.
     */
    private static function generate_resized_icon($source_url, $width, $height)
    {
        // Ensure the image editor is available
        if (!wp_image_editor_supports()) {
            return false;
        }

        // Fetch the image content using wp_remote_get to handle SSL issues
        $response = wp_remote_get($source_url, [
            'sslverify' => false, // Disable SSL verification for local development
            'timeout' => 15,
        ]);

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            // Log the error for debugging
            // error_log('Q_PWA_Manifest: Failed to fetch image for resizing: ' . $source_url . ' - ' . (is_wp_error($response) ? $response->get_error_message() : wp_remote_retrieve_response_code($response)));
            return false;
        }

        $image_content = wp_remote_retrieve_body($response);

        // Save the image content to a temporary file
        $upload_dir = wp_upload_dir();
        $temp_filename = 'pwa-temp-icon-' . uniqid() . '.png';
        $temp_filepath = trailingslashit($upload_dir['path']) . $temp_filename;

        if (file_put_contents($temp_filepath, $image_content) === false) {
            // error_log('Q_PWA_Manifest: Failed to save temporary image file: ' . $temp_filepath);
            return false;
        }

        // Use wp_get_image_editor with the local temporary file
        $editor = wp_get_image_editor($temp_filepath);

        // Clean up the temporary file immediately after getting the editor
        unlink($temp_filepath);

        if (is_wp_error($editor)) {
            // error_log('Q_PWA_Manifest: Failed to get image editor for temporary file: ' . $temp_filepath . ' - ' . $editor->get_error_message());
            return false;
        }

        $editor->resize($width, $height, true);

        // Save the resized image
        $filename = 'pwa-icon-' . $width . 'x' . $height . '-' . uniqid() . '.png';
        $filepath = trailingslashit($upload_dir['path']) . $filename;
        $fileurl = trailingslashit($upload_dir['url']) . $filename;

        $saved = $editor->save($filepath, 'image/png');

        if (is_wp_error($saved)) {
            // error_log('Q_PWA_Manifest: Failed to save resized image: ' . $filepath . ' - ' . $saved->get_error_message());
            return false;
        }

        return [
            'src'    => $fileurl,
            'sizes'  => $width . 'x' . $height,
            'type'   => 'image/png',
            'purpose'=> 'any', // Assuming 'any' purpose for generated icons
        ];
    }

    private static function icon_size_exists($icons, $size)
    {
        foreach ($icons as $icon) {
            if ($icon['sizes'] === $size) {
                return true;
            }
        }
        return false;
    }

    public static function generate_screenshots()
    {
        $screenshots = [];

        // Prefer the splash screen, otherwise fall back to the 512-icon variant.
        $source = get_option('q_pwa_splash', '');
        if (empty($source)) {
            $source = get_option('q_pwa_icon_512', '');
        }

        if (empty($source)) {
            return $screenshots; // Nothing to add
        }

        if (!preg_match('/^https?:\/\//', $source)) {
            $source = site_url($source);
        }

        [$width, $height] = self::detect_image_size($source);
        if (!$width || !$height) {
            return $screenshots; // Could not determine size
        }

        $sizes  = $width . 'x' . $height;

        // Always provide both required form factors (wide & narrow) – can reuse same image.
        $screenshots[] = [
            'src'         => $source,
            'sizes'       => $sizes,
            'type'        => 'image/png',
            'form_factor' => 'wide',
            'label'       => 'App – Wide',
        ];

        $screenshots[] = [
            'src'         => $source,
            'sizes'       => $sizes,
            'type'        => 'image/png',
            'form_factor' => 'narrow',
            'label'       => 'App – Narrow',
        ];

        return $screenshots;
    }

    public static function validate_manifest()
    {
        $manifest = self::generate_manifest();
        $errors = [];

        // Check required fields
        $required_fields = ['name', 'short_name', 'start_url', 'display'];
        foreach ($required_fields as $field) {
            if (empty($manifest[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }

        // Validate name length
        if (strlen($manifest['name']) > 45) {
            $errors[] = "App name should be 45 characters or less";
        }

        // Validate short_name length
        if (strlen($manifest['short_name']) > 12) {
            $errors[] = "Short name should be 12 characters or less";
        }

        // Validate start_url
        if (!filter_var($manifest['start_url'], FILTER_VALIDATE_URL) && !preg_match('/^\//', $manifest['start_url'])) {
            $errors[] = "Start URL must be a valid URL or start with /";
        }

        // Validate display mode
        $valid_display_modes = ['fullscreen', 'standalone', 'minimal-ui', 'browser'];
        if (!in_array($manifest['display'], $valid_display_modes)) {
            $errors[] = "Invalid display mode";
        }

        // Validate theme color
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $manifest['theme_color'])) {
            $errors[] = "Theme color must be a valid hex color";
        }

        // Validate background color
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $manifest['background_color'])) {
            $errors[] = "Background color must be a valid hex color";
        }

        // Check for icons
        if (empty($manifest['icons'])) {
            $errors[] = "At least one icon is required";
        } else {
            // Validate icon sizes
            $has_192 = false;
            $has_512 = false;

            foreach ($manifest['icons'] as $icon) {
                if (strpos($icon['sizes'], '192x192') !== false) {
                    $has_192 = true;
                }
                if (strpos($icon['sizes'], '512x512') !== false) {
                    $has_512 = true;
                }
            }

            if (!$has_192) {
                $errors[] = "192x192 icon is recommended";
            }
            if (!$has_512) {
                $errors[] = "512x512 icon is recommended";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'manifest' => $manifest
        ];
    }

    public static function get_manifest_url()
    {
        return home_url('/manifest.json');
    }

    public static function flush_manifest_cache()
    {
        // Update the manifest timestamp to force browsers to fetch a new version
        update_option('q_pwa_manifest_updated', time());
        
        // Clear any caching if needed
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Flush rewrite rules to ensure manifest route works
        flush_rewrite_rules();
        
        return true;
    }

    public static function install_manifest_route()
    {
        self::add_manifest_rewrite_rule();
        flush_rewrite_rules();
    }

    public static function uninstall_manifest_route()
    {
        // Remove rewrite rules on deactivation
        flush_rewrite_rules();
    }

    /**
     * Get app categories based on settings
     */
    public static function get_app_categories()
    {
        $default_categories = ['productivity', 'utilities'];
        $custom_categories = get_option('q_pwa_categories', '');

        if (!empty($custom_categories)) {
            $categories = array_map('trim', explode(',', $custom_categories));
            return array_filter($categories);
        }

        return $default_categories;
    }

    /**
     * Add app shortcuts to manifest
     */
    public static function add_shortcuts($manifest)
    {
        if (!get_option('q_pwa_shortcuts_enabled', false)) {
            return $manifest;
        }

        $shortcuts = [];
        $scope = $manifest['scope'] ?? '/';
        $start_url = $manifest['start_url'] ?? '/';
        $site_url = site_url();

        // Get configured shortcuts (up to 4 as per spec)
        for ($i = 1; $i <= 4; $i++) {
            $name = get_option("q_pwa_shortcut_{$i}_name", '');
            $url = get_option("q_pwa_shortcut_{$i}_url", '');
            $description = get_option("q_pwa_shortcut_{$i}_description", '');
            $icon = get_option("q_pwa_shortcut_{$i}_icon", '');

            if (!empty($name) && !empty($url)) {
                // Ensure URL starts with / to be within scope
                if (!preg_match('/^\//', $url)) {
                    $url = '/' . $url;
                }
                
                // Add special parameter to indicate this is a PWA shortcut
                $url = add_query_arg('pwa_shortcut', '1', $url);
                
                $shortcut = [
                    'name' => $name,
                    'short_name' => $name,
                    'url' => $url,
                    'description' => $description ?: $name
                ];

                if (!empty($icon)) {
                    // Ensure icon URL is absolute
                    if (!preg_match('/^https?:\/\//', $icon)) {
                        $icon = site_url($icon);
                    }
                    
                    $shortcut['icons'] = [
                        [
                            'src' => $icon,
                            'sizes' => '96x96',
                            'type' => 'image/png'
                        ]
                    ];
                }

                // Add shortcut ID for analytics tracking
                $shortcut['id'] = 'shortcut-' . $i;

                $shortcuts[] = $shortcut;
            }
        }

        // Add default shortcuts if none are configured
        if (empty($shortcuts)) {
            // Home shortcut
            $shortcuts[] = [
                'name' => 'Home',
                'short_name' => 'Home',
                'description' => 'Go to the homepage',
                'url' => '/?pwa_shortcut=1',
                'id' => 'shortcut-home',
                'icons' => [
                    [
                        'src' => $manifest['icons'][0]['src'] ?? '',
                        'sizes' => '96x96',
                        'type' => 'image/png'
                    ]
                ]
            ];
            
            // Check if WooCommerce is active
            if (class_exists('WooCommerce')) {
                // Add shop shortcut
                $shortcuts[] = [
                    'name' => 'Shop',
                    'short_name' => 'Shop',
                    'description' => 'Browse products',
                    'url' => '/shop/?pwa_shortcut=1',
                    'id' => 'shortcut-shop'
                ];
                
                // Add cart shortcut
                $shortcuts[] = [
                    'name' => 'Cart',
                    'short_name' => 'Cart',
                    'description' => 'View your cart',
                    'url' => '/cart/?pwa_shortcut=1',
                    'id' => 'shortcut-cart'
                ];
            }
        }

        if (!empty($shortcuts)) {
            $manifest['shortcuts'] = $shortcuts;
        }

        return $manifest;
    }

    /**
     * Add share target configuration
     */
    public static function add_share_target($manifest)
    {
        if (!get_option('q_pwa_share_target_enabled', false)) {
            return $manifest;
        }

        $share_target_url = get_option('q_pwa_share_target_url', '/share');

        // Always enable share target functionality
        $manifest['share_target'] = [
            'action' => $share_target_url,
            'method' => 'POST',
            'enctype' => 'multipart/form-data',
            'params' => [
                'title' => 'title',
                'text' => 'text',
                'url' => 'url'
            ]
        ];

        // Add file sharing with expanded file types
        if (get_option('q_pwa_share_files_enabled', false)) {
            // Get file types or use comprehensive defaults
            $file_types = get_option('q_pwa_share_file_types', '');
            
            if (empty($file_types)) {
                // Provide comprehensive defaults for common file types
                $file_types = [
                    'image/*',
                    'video/*',
                    'audio/*',
                    'text/*',
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                ];
            } else {
                $file_types = array_map('trim', explode(',', $file_types));
            }
            
            // Add file handling for different categories
            $manifest['share_target']['params']['files'] = [
                [
                    'name' => 'images',
                    'accept' => ['image/*']
                ],
                [
                    'name' => 'videos',
                    'accept' => ['video/*']
                ],
                [
                    'name' => 'documents',
                    'accept' => [
                        'text/*',
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.ms-excel',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    ]
                ]
            ];
        }

        return $manifest;
    }

    /**
     * Add file handlers
     */
    public static function add_file_handlers($manifest)
    {
        if (!get_option('q_pwa_file_handlers_enabled', false)) {
            return $manifest;
        }

        $file_handlers = [];
        $handler_types = get_option('q_pwa_file_handler_types', 'text/plain,text/csv');

        if (!empty($handler_types)) {
            $types = array_map('trim', explode(',', $handler_types));

            foreach ($types as $type) {
                $file_handlers[] = [
                    'action' => '/handle-file',
                    'accept' => [$type]
                ];
            }
        }

        if (!empty($file_handlers)) {
            $manifest['file_handlers'] = $file_handlers;
        }

        return $manifest;
    }

    /**
     * Add protocol handlers
     */
    public static function add_protocol_handlers($manifest)
    {
        if (!get_option('q_pwa_protocol_handlers_enabled', false)) {
            return $manifest;
        }

        $protocol_handlers = [];
        $protocols = get_option('q_pwa_protocol_list', '');

        if (!empty($protocols)) {
            $protocol_list = array_map('trim', explode(',', $protocols));

            foreach ($protocol_list as $protocol) {
                if (!empty($protocol)) {
                    $protocol_handlers[] = [
                        'protocol' => $protocol,
                        'url' => "/handle-protocol?url=%s"
                    ];
                }
            }
        }

        if (!empty($protocol_handlers)) {
            $manifest['protocol_handlers'] = $protocol_handlers;
        }

        return $manifest;
    }

    /**
     * Add display override for enhanced display modes
     */
    public static function add_display_override($manifest)
    {
        $display_override = get_option('q_pwa_display_override', '');

        if (!empty($display_override)) {
            $overrides = array_map('trim', explode(',', $display_override));
            $manifest['display_override'] = array_filter($overrides);
        }
        
        // Add window-controls-overlay if enabled
        if (get_option('q_pwa_window_controls_overlay', false)) {
            if (!isset($manifest['display_override']) || !is_array($manifest['display_override'])) {
                $manifest['display_override'] = [];
            }
            
            // Add window-controls-overlay as the first option for highest priority
            array_unshift($manifest['display_override'], 'window-controls-overlay');
            
            // Add window-controls-overlay-caption-button-close property if specified
            $caption_button_close = get_option('q_pwa_wco_caption_button_close', '');
            if (!empty($caption_button_close)) {
                $manifest['window-controls-overlay-caption-button-close'] = $caption_button_close;
            }
        }

        return $manifest;
    }

    /**
     * Add launch handler configuration – improves the desktop PWA experience by
     * ensuring new navigations focus an existing client instead of spawning
     * additional windows. This reduces clutter and mirrors the behaviour of
     * traditional desktop apps. The default behaviour can be overridden via a
     * filter or a dedicated option in the future.
     *
     * @param array $manifest The current manifest array.
     * @return array          The augmented manifest array.
     */
    public static function add_launch_handler($manifest)
    {
        // Allow devs/admins to disable this feature if needed
        $enabled = apply_filters('q_pwa_launch_handler_enabled', true);

        if (!$enabled) {
            return $manifest;
        }

        // Determine the desired client_mode.  The spec currently supports
        // "focus-existing", "navigate-existing", and "auto".  We default to
        // focus-existing which will focus a previously opened window if one
        // exists, otherwise open a new one.
        $client_mode = apply_filters('q_pwa_launch_handler_client_mode', 'focus-existing');

        // Append the launch_handler block to the manifest
        $manifest['launch_handler'] = [
            'client_mode' => $client_mode
        ];

        return $manifest;
    }

    private static function detect_image_size($url)
    {
        // First try the URL directly (works when allow_url_fopen is enabled)
        $info = @getimagesize($url);
        if ($info && isset($info[0], $info[1])) {
            return [$info[0], $info[1]];
        }

        // If that failed and the URL is inside this WP install, convert to a local path.
        $site_url = site_url();
        if (strpos($url, $site_url) === 0) {
            $relative_path = substr($url, strlen($site_url));
            $local_path    = ABSPATH . ltrim($relative_path, '/');
            if (file_exists($local_path)) {
                $info = @getimagesize($local_path);
                if ($info && isset($info[0], $info[1])) {
                    return [$info[0], $info[1]];
                }
            }
        }

        return [0, 0]; // Unknown size
    }

}
